import "dotenv/config";
import express from 'express';
import session from 'express-session';
import cookieParser from 'cookie-parser';
import logger from 'morgan';
import helmet from 'helmet';
import createError from "http-errors";
import router from './routes/route.js';

const app = express();

app.disable("x-powered-by");
app.set("port", process.env.PORT || 6017);
app.use(helmet());
app.use(logger("dev"));
app.use(session({
    secret: process.env.APISECRET,
    saveUninitialized: true,
    resave: true
}));
app.use(cookieParser());
app.use(express.json());
app.use(express.urlencoded({
    extended: true
}));

/* Routes */
app.use('/SAP/PS/Project', router);

/* When any Invalid Path Provided. */
app.use((req, res, next) => {
    next(createError(404, `Path '${req.path}' Not found`));
});

/* An error handling middleware. */
app.use((error, req, res, next) => {
    res.status(error.status || 500).send({ Error: error.message });
});

/* Instantiate the server */
const port = app.get("port");
app.listen(port, () => console.log(`Server listening on port ${port}`));