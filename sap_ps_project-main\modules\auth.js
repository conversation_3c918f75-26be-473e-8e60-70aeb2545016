import "dotenv/config";
import { expressjwt } from "express-jwt";
import JwksRsa from "jwks-rsa";
import { Client as rfcClient } from "node-rfc";
import createError from "http-errors";
import abapsystem from "../modules/abapsystem.js";

const extractTokenFromHeader = (req) => {
    if (req?.cookies?.MATWGLTOKEN) {
        return req?.cookies?.MATWGLTOKEN;
    } else if (req?.headers?.authorization && req?.headers?.authorization.split(" ")[0] === "Bearer") {
        return req?.headers?.authorization.split(" ")[1];
    }
    return null;
}
//next(createError(400, result?.error?.details[0]?.message)) // res.status(401).send(err.message)
const verifytoken = (req, res, next) => {
    if (req?.headers?.authorization && req?.headers?.authorization.split(" ")[0] === "Basic") {
        //create new sap-client for user authentication
        const abapClient = new rfcClient(abapsystem(req));
        abapClient.connect((err) => {
            if (err) {
                next(createError(401, err?.message))
            } else {
                res.setHeader("sapclient", `${abapClient?.connectionInfo?.sysId}${abapClient?.connectionInfo?.client}`)
                next()
            }
        });
    } else {
        return expressjwt({ //Token Based Authentication
            secret: JwksRsa.expressJwtSecret({
                cache: true,
                rateLimit: true,
                jwksRequestsPerMinute: 5,
                jwksUri: process.env.JWTLINK,
            }),
            getToken: extractTokenFromHeader,
            requestProperty: "matwtoken",
            algorithms: ["RS256"],
        }).unless({})(req, res, next);
    }
};

export default verifytoken;