import express from 'express';
import verifytoken from '../modules/auth.js';
import sapsdclient from "../modules/sappsclient.js";

const router = express.Router();

// Get Delivery Details
router.get("/PayloadDetails", verifytoken, (req, res, next) => {
    const sapSd = new sapsdclient(req);
    sapSd.GetProjectPayload(req.query)
        .then(retn => res.status(200).send(retn))
        .catch(error => next(error));
});

// Get Project Defination
router.get("/Definition", verifytoken, (req, res, next) => {
    const sapSd = new sapsdclient(req);
    sapSd.validateProjectDefinition(req.query)
        .then(retn => sapSd.getProjectDefinition(retn))
        .then(retn => res.status(200).send(retn))
        .catch(error => next(error));
});

// Get Project WBS Element
router.get("/WBSElements", verifytoken, (req, res, next) => {
    const sapSd = new sapsdclient(req);
    sapSd.validateProjectDefinition(req.query)
        .then(retn => sapSd.getWBSElement(retn))
        .then(retn => res.status(200).send(retn))
        .catch(error => next(error));
});

// Get Project WBS Network
router.get("/Network", verifytoken, (req, res, next) => {
    const sapSd = new sapsdclient(req);
    sapSd.validateNetworkNumber(req.query)
        .then(retn => sapSd.GetWbsNetwork(retn))
        .then(retn => res.status(200).send(retn))
        .catch(error => next(error));
});

export default router;