{"Network": {"NETWORK": "Network number", "NETWORK_TYPE": "Network type", "SHORT_TEXT": "Description", "PLANT": "Plant", "PROFIT_CTR": "Profit Center", "WBS_ELEMENT": "Work Breakdown Structure Element (WBS Element)", "TAXJURCODE": "Tax Jurisdiction", "OBJECTCLASS": "Object class (language-independent)", "FINISH_DATE": "Basic finish date", "START_DATE": "Basic start date", "MRP_CONTROLLER": "MRP controller for the order", "SCHED_TYPE": "Scheduling type", "PRIORITY": "Order priority", "PROJECT_DEFINITION": "Project Definition", "NOT_AUTO_SCHEDULE": "Indicator: Do not schedule automatically", "NOT_AUTO_COSTING": "Indicator: Do not cost automatically", "NOT_MRP_APPLICABLE": "Ind: Reserv. not applicable to MRP;Purc. req. not created", "PROFILE": "Network profile", "DELETION_FLAG": "Deletion Indicator", "OBJECTCLASS_EXT": "Object class, language-dependent", "SCHED_START_DATE": "Scheduled start", "SCHED_FINISH_DATE": "Scheduled finish", "SCHED_RELEASE_DATE": "Scheduled release date", "SCHED_TYPE_FORECAST": "Scheduling type (forecast)", "START_DATE_FORECAST": "Forecast start date", "FINISH_DATE_FORECAST": "Finish date (forecast)", "SCHED_START_DATE_FORECAST": "Scheduled forecast start", "SCHED_FINISH_DATE_FORECAST": "Scheduled forecast finish", "SCHED_RELEASE_DATE_FORECAST": "Scheduled release date (forecast)", "ACTUAL_START_DATE": "Actual start date", "CONFIRMED_FINISH_DATE": "Confirmed Order Finish Date", "ACTUAL_RELEASE_DATE": "Actual release date", "FUNC_AREA": "Functional Area", "FUNC_AREA_LONG": "Functional Area", "PLANNED_COST": "Planned Costs", "ACTUAL_COST": "Actual Costs", "CONTROLLING_AREA_CURRENCY": "<PERSON><PERSON><PERSON><PERSON> Key", "ACTIVITY_ACCOUNT_ASSIGNED": "Network: Activity-Assigned"}}