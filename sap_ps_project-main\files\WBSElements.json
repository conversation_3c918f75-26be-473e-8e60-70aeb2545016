{"WBS_Elements": [{"WBS_ELEMENT": "Work Breakdown Structure Element (WBS Element)", "DESCRIPTION": "PS: Short description (1st text line)", "RESPONSIBLE_NO": "Number of the responsible person (project manager)", "APPLICANT_NO": "Applicant number", "COMPANY_CODE": "Company code for WBS element", "BUSINESS_AREA": "Business area for WBS element", "CONTROLLING_AREA": "Controlling area for WBS element", "PROFIT_CTR": "Profit Center", "PROJ_TYPE": "Project type", "WBS_PLANNING_ELEMENT": "Indicator: Planning element", "WBS_ACCOUNT_ASSIGNMENT_ELEMENT": "Indicator: Account assignment element", "WBS_BILLING_ELEMENT": "Indicator: Billing element", "CSTG_SHEET": "Costing Sheet", "OVERHEAD_KEY": "Overhead key", "RES_ANAL_KEY": "Results Analysis Key", "REQUEST_CCTR_CONTROLLING_AREA": "Controlling area of requesting cost center", "REQUEST_CCTR": "Requesting cost center", "RESPSBL_CCTR_CONTROLLING_AREA": "Controlling area of responsible cost center", "RESPSBL_CCTR": "Responsible cost center", "CALENDAR": "Factory calendar key", "PRIORITY": "Priority", "EQUIPMENT": "Equipment Number", "FUNCT_LOC": "Functional Location", "CURRENCY": "WBS element currency", "CURRENCY_ISO": "ISO currency code", "PLANT": "Plant", "USER_FIELD_KEY": "Key word ID for user-defined fields", "USER_FIELD_CHAR20_1": "User field with 20 characters", "USER_FIELD_CHAR20_2": "User field with 20 characters", "USER_FIELD_CHAR10_1": "User field with 10 characters", "USER_FIELD_CHAR10_2": "User field with 10 characters", "USER_FIELD_QUAN1": "User field for quantity (length 10.3)", "USER_FIELD_UNIT1": "User field: Unit for quantity fields", "USER_FIELD_UNIT1_ISO": "ISO code for unit of measurement", "USER_FIELD_QUAN2": "User field for quantity (length 10.3)", "USER_FIELD_UNIT2": "User field: Unit for quantity fields", "USER_FIELD_UNIT2_ISO": "ISO code for unit of measurement", "USER_FIELD_CURR1": "User-defined field for values (length 10,3)", "USER_FIELD_CUKY1": "User field: Unit for value fields", "USER_FIELD_CUKY1_ISO": "ISO currency code", "USER_FIELD_CURR2": "User-defined field for values (length 10,3)", "USER_FIELD_CUKY2": "User field: Unit for value fields", "USER_FIELD_CUKY2_ISO": "ISO currency code", "USER_FIELD_DATE1": "User field for date", "USER_FIELD_DATE2": "User field for date", "USER_FIELD_FLAG1": "User-defined field: Indicator for reports", "USER_FIELD_FLAG2": "User-defined field: Indicator for reports", "WBS_CCTR_POSTED_ACTUAL": "Cost center to which costs are actually posted", "WBS_SUMMARIZATION": "Indicator: WBS element used in project summarization", "OBJECTCLASS": "Object class, language-dependent", "STATISTICAL": "Statistical WBS element", "TAXJURCODE": "Tax Jurisdiction", "INTEREST_PROF": "Interest Profile for Project/Order Interest Calculation", "INVEST_PROFILE": "Investment measure profile", "EVGEW": "Aggregation weight for POC (PS progress)", "CHANGE_NO": "Change Number", "SUBPROJECT": "Sub-project in work breakdown structure", "PLANINTEGRATED": "Indicator for Integrated Planning", "INV_REASON": "Reason for investment", "SCALE": "Scale of investment objects", "ENVIR_INVEST": "Reason for environmental investment", "REQUEST_COMP_CODE": "Requesting company code", "WBS_MRP_ELEMENT": "Indicator: Grouping WBS element", "LOCATION": "Location", "VENTURE": "Joint Venture", "REC_IND": "Recovery Indicator", "EQUITY_TYP": "Equity Type", "JV_OTYPE": "Joint Venture Object Type", "JV_JIBCL": "JIB/JIBE Class", "JV_JIBSA": "JIB/JIBE Subclass A", "WBS_BASIC_START_DATE": "WBS element: Basic start date", "WBS_BASIC_FINISH_DATE": "WBS element: Basic finish date", "WBS_FORECAST_START_DATE": "Forecasted start date of the WBS element", "WBS_FORECAST_FINISH_DATE": "Forecasted finish date of the WBS element", "WBS_ACTUAL_START_DATE": "Actual start date for the WBS element", "WBS_ACTUAL_FINISH_DATE": "Actual finish date of the WBS element", "WBS_BASIC_DURATION": "Length (duration) of the basic dates in the WBS element", "WBS_BASIC_DUR_UNIT": "Unit for duration of basic WBS element dates", "WBS_BASIC_DUR_UNIT_ISO": "ISO code for unit of measurement", "WBS_FORECAST_DURATION": "Forecasted length (duration) of the WBS element", "WBS_FORCAST_DUR_UNIT": "Unit for duration of WBS element planned dates", "WBS_FORECAST_DUR_UNIT_ISO": "ISO code for unit of measurement", "WBS_ACTUAL_DURATION": "Actual length (duration) of the WBS element", "WBS_ACTUAL_DUR_UNIT": "Unit for duration of actual WBS element dates", "WBS_ACTUAL_DUR_UNIT_ISO": "ISO code for unit of measurement", "WBS_SCD_BASIC_START_DATE": "Earliest scheduled start date (Basic)", "WBS_SCD_BASIC_FINISH_DATE": "Latest scheduled finish date (basic)", "WBS_SCD_FORECAST_START_DATE": "Scheduled start date (Forecast)", "WBS_SCD_FORECAST_FINISH_DATE": "Latest scheduled  finish date (Forecast)", "WBS_SCD_ACTUAL_START_DATE": "Tentative actual start date for WBS element", "WBS_SCD_ACTUAL_FINISH_DATE": "Tentative actual finish date for WBS element", "WBS_UP": "Work Breakdown Structure Element (WBS Element)", "WBS_DOWN": "Work Breakdown Structure Element (WBS Element)", "WBS_LEFT": "Work Breakdown Structure Element (WBS Element)", "WBS_RIGHT": "Work Breakdown Structure Element (WBS Element)", "FUNC_AREA": "Functional Area", "LANGU": "Language Key", "LANGU_ISO": "2-Character SAP Language Code", "SYSTEM_STATUS": "System Status Text", "SHORT_ID": "WBS element short identification", "NETWORK_ASSIGNMENT": "Network assignment", "PROJECT_DEFINITION": "Project Definition", "STAT_PROF": "Status Profile", "NETWORK": "Network Number"}]}