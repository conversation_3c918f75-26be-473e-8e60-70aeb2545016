import fs from "fs";
import createError from "http-errors";
import { Client as rfcClient } from "node-rfc";
import abapsystem from "./abapsystem.js";
import joischema from "../schema/sappsschema.js";

export default class sapClient {
    // SAP Client
    #abapClient

    // Constructor Method
    constructor(req) {
        // create new sap-client 
        this.#abapClient = new rfcClient(abapsystem(req));
    }

    // SAP RFC Function Calls
    callRfc(fmname, fmexport) {
        return new Promise(async (resolve, reject) => {
            try {
                await this.#abapClient.open();
                const fmimport = await this.#abapClient.call(fmname, fmexport);
                await this.#abapClient.close();
                resolve(fmimport)
            } catch (error) {
                reject(createError(400, error?.message))
            }
        })
    }

    //Validate Payload Query
    GetProjectPayload(query) {
        return new Promise(async (resolve, reject) => {
            const result = joischema.ProjectPayload.validate(query);
            if (result?.error) {
                reject(createError(400, result?.error?.details[0]?.message));
            } else {
                const jsonpayload = JSON.parse(fs.readFileSync(`files/${result?.value?.Payload}.json`));
                resolve(jsonpayload);

                //***************************************************Comment Below */
                // const {
                //     DFIES_TAB: dfies
                // } = await this.callRfc("DDIF_FIELDINFO_GET", {
                //     TABNAME: "BAPI_NETWORK_EXP"
                // });
                // const final_Structure = {};
                // dfies.forEach(elm => {
                //     final_Structure[elm.FIELDNAME] = elm.FIELDTEXT
                // })
                // resolve(final_Structure);
                //***************************************************Comment Below */
            }
        })
    }

    // VAlidate Query Parameter
    validateProjectDefinition(reqQuery) {
        return new Promise(async (resolve, reject) => {
            const result = joischema.ProjectDefinitionQuery.validate(reqQuery);
            if (result?.error) {
                reject(createError(400, result?.error?.details[0]?.message));
            } else {
                resolve(result?.value);
            }
        })
    }

    getProjectDefinition(reqQuery) {
        return new Promise(async (resolve, reject) => {
            try {
                // Retrieve Project Defination
                const {
                    E_PROJECT_DEFINITION: Definition,
                    ET_RETURN: Return,
                    EXTENSIONOUT
                } = await this.callRfc("BAPI_BUS2001_GETDATA", {
                    I_PROJECT_DEFINITION: reqQuery.ProjectDefinition,
                    I_LANGUAGE: "EN"
                });

                // Retrieve Project-Defination Custom Extended fields
                if (EXTENSIONOUT[0]?.STRUCTURE === "BAPI_TE_PROJECT_DEFINITION") {
                    Definition.Extension = {
                        ZZKUNNR: EXTENSIONOUT[0].VALUEPART1.slice(24, 34),
                        ZZNAME1: EXTENSIONOUT[0].VALUEPART1.slice(34, 69),
                        ZZEXT_REFID: EXTENSIONOUT[0].VALUEPART1.slice(69, 99)
                    }
                }

                if (Definition.PROJECT_DEFINITION) {
                    //Retrive PROJ Data
                    const { DATA: proj } = await this.callRfc("RFC_READ_TABLE", {
                        QUERY_TABLE: "PROJ",
                        DELIMITER: "|",
                        FIELDS: [
                            { FIELDNAME: "PSPNR", OFFSET: "000000", LENGTH: "000008", TYPE: "C" },
                            { FIELDNAME: "PSPID", OFFSET: "000009", LENGTH: "000024", TYPE: "C" },
                            { FIELDNAME: "OBJNR", OFFSET: "000034", LENGTH: "000022", TYPE: "C" }
                        ],
                        OPTIONS: [`PSPID EQ '${Definition.PROJECT_DEFINITION}'`]
                    });

                    //Retrive IHPA Data
                    const { DATA: ihpa } = await this.callRfc("RFC_READ_TABLE", {
                        QUERY_TABLE: "IHPA",
                        DELIMITER: "|",
                        FIELDS: [
                            { FIELDNAME: "OBJNR", OFFSET: "000000", LENGTH: "000022", TYPE: "C" },
                            { FIELDNAME: "PARVW", OFFSET: "000023", LENGTH: "000002", TYPE: "C" },
                            { FIELDNAME: "COUNTER", OFFSET: "000026", LENGTH: "000006", TYPE: "C" },
                            { FIELDNAME: "OBTYP", OFFSET: "000033", LENGTH: "000003", TYPE: "C" },
                            { FIELDNAME: "PARNR", OFFSET: "000037", LENGTH: "000012", TYPE: "C" }
                        ],
                        OPTIONS: [`OBJNR EQ '${proj[0].WA.slice(34, 56)}'`]
                    });

                    if (ihpa.length > 0) {
                        Definition.Partners = []
                        ihpa.forEach(p => {
                            Definition.Partners.push({
                                ObjectNumber: p.WA.slice(0, 22).trimEnd(),
                                PartnerFunction: p.WA.slice(23, 25),
                                Counter: p.WA.slice(26, 32),
                                ObjectCategory: p.WA.slice(33, 36),
                                Partner: p.WA.slice(37, 49)
                            })
                        })
                    }

                    // Project Definition Read Text
                    const {
                        TEXT_LINES: LongText
                    } = await this.callRfc("RFC_READ_TEXT", {
                        TEXT_LINES: [{
                            "TDOBJECT": "PMS",
                            "TDNAME": proj[0].WA.slice(35, 56).trimEnd(),
                            "TDID": "LTXT",
                            "TDSPRAS": "EN",
                        }]
                    });
                    Definition.LongText = LongText.map((obj) => obj.TDLINE).join();
                }

                resolve({
                    Definition: Definition.PROJECT_DEFINITION ? Definition : Return[-1],
                    Return: Definition.PROJECT_DEFINITION ? Return[-1] : Return
                });

            } catch (error) {
                reject(createError(400, error?.message))
            }
        })
    }

    getWBSElement(reqQuery) {
        return new Promise(async (resolve, reject) => {
            try {
                // Retrieve WBS Element
                const {
                    ET_WBS_ELEMENT: WBS_Elements,
                    ET_RETURN: Return
                    // EXTENSIONOUT - No Custom fields yet
                } = await this.callRfc("BAPI_BUS2054_GETDATA", {
                    I_PROJECT_DEFINITION: reqQuery.ProjectDefinition,
                    I_LANGUAGE: "EN"
                });

                // Retrieve Network Details
                if (WBS_Elements[0]) {
                    //Retrive PRPS Data ------------------------------------------------------------------
                    const fmExpPrps = {
                        QUERY_TABLE: "PRPS",
                        DELIMITER: "|",
                        FIELDS: [
                            { FIELDNAME: "PSPNR", OFFSET: "000000", LENGTH: "000008", TYPE: "C" },
                            { FIELDNAME: "POSID", OFFSET: "000009", LENGTH: "000024", TYPE: "C" }
                        ],
                        OPTIONS: ["POSID IN ( "]
                    }
                    WBS_Elements.forEach((elm, i) => fmExpPrps.OPTIONS.push(`${i !== 0 ? "," : ""}'${elm?.WBS_ELEMENT}'`));
                    fmExpPrps.OPTIONS.push(" )");
                    const { DATA: prps } = await this.callRfc("RFC_READ_TABLE", fmExpPrps);

                    //Retrive AUFK Data ------------------------------------------------------------------
                    const fmExpAufk = {
                        QUERY_TABLE: "AUFK",
                        DELIMITER: "|",
                        FIELDS: [
                            { FIELDNAME: "AUFNR", OFFSET: "000000", LENGTH: "000012", TYPE: "C" },
                            { FIELDNAME: "AUART", OFFSET: "000013", LENGTH: "000004", TYPE: "C" },
                            { FIELDNAME: "AUTYP", OFFSET: "000018", LENGTH: "000002", TYPE: "C" },
                            { FIELDNAME: "PSPEL", OFFSET: "000021", LENGTH: "000008", TYPE: "C" }
                        ],
                        OPTIONS: ["AUTYP EQ '20' AND PSPEL IN ( "]
                    }
                    prps.forEach((e, i) => fmExpAufk.OPTIONS.push(`${i !== 0 ? "," : ""}'${e.WA.slice(0, 8)}'`));
                    fmExpAufk.OPTIONS.push(" )");
                    const { DATA: aufk } = await this.callRfc("RFC_READ_TABLE", fmExpAufk);

                    //Prepare Network Number
                    aufk.forEach(e => {
                        const wbsname = prps.find(elm => elm.WA.slice(0, 8) === e.WA.slice(21, 30));
                        if (wbsname) {
                            const wbsIndex = WBS_Elements.findIndex(el => el.WBS_ELEMENT === wbsname.WA.slice(9, 24));
                            if (wbsIndex !== -1) {
                                WBS_Elements[wbsIndex].NETWORK = e.WA.slice(0, 12);
                            }
                        }
                    })
                }

                resolve({
                    WBS_Elements: WBS_Elements[0] ? WBS_Elements : WBS_Elements[0],
                    Return: WBS_Elements[0] ? Return[-1] : Return
                });

            } catch (error) {
                reject(createError(400, error?.message))
            }
        })
    }

    // VAlidate Query Parameter
    validateNetworkNumber(reqQuery) {
        return new Promise(async (resolve, reject) => {
            const result = joischema.ProjectNetworkQuery.validate(reqQuery);
            if (result?.error) {
                reject(createError(400, result?.error?.details[0]?.message));
            } else {
                resolve(result?.value);
            }
        })
    }

    GetWbsNetwork(reqQuery) {
        return new Promise(async (resolve, reject) => {
            try {
                const {
                    E_MESSAGE_TABLE: Message,
                    E_NETWORK: Networks
                } = await this.callRfc("BAPI_NETWORK_GETINFO", {
                    I_NETWORK_LIST: [{ NETWORK: reqQuery.Network }]
                });

                resolve({
                    Network: Networks[0],
                    Message: Networks[0] ? Message[0] : Message
                });

            } catch (error) {
                reject(createError(400, error?.message))
            }
        })
    }

}