version: "3.3"
services:
  sapprojectsystem:
    container_name: sap_project_system
    build: .
    image: matw/sap_project_system
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 3G
        reservations:
          cpus: '0.25'
          memory: 2G
    ports:
      - "6017:6017"
    env_file:
      - "./config/.env"
    environment:
      - PORT=6017
      - ASHOST=ud3-sv-sapec3.matw.matthewsintl.com
      - SYSNR=00
      - CLIENT=300
      - LANGU=EN
      - ABAPSYS=NP1
      - RFC_TRACE=0
      - JWTLINK=http://c3mft01lp.amer.schawk.com:8082/auth/.well-known/jwks.json
      - JWTLOGIN=http://c3mft01lp.amer.schawk.com:8082/auth/login
    labels:
      - "co.elastic.logs/enabled=true"
      - "co.elastic.logs/fileset.stdout=access"
      - "co.elastic.logs/fileset.stderr=error"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
