{"Definition": {"PROJECT_DEFINITION": "Project Definition", "DESCRIPTION": "PS: Short description (1st text line)", "MASK_ID": "Selection mask for WBS element short IDs", "WBS_STATUS_PROFILE": "Status profile for WBS element", "RESPONSIBLE_NO": "Number of the responsible person (project manager)", "APPLICANT_NO": "Applicant number", "COMPANY_CODE": "Company code for the project", "BUSINESS_AREA": "Business area for the project", "CONTROLLING_AREA": "Controlling area for the project", "PROFIT_CTR": "Profit Center", "PROJECT_CURRENCY": "WBS currency (project definition)", "PROJECT_CURRENCY_ISO": "ISO currency code", "START": "Project planned start date", "FINISH": "Project planned finish date", "PLANT": "Plant", "CALENDAR": "Factory calendar key", "PLAN_BASIC": "Planning method for project basic dates", "PLAN_FCST": "Planning method for project forecast dates", "TIME_UNIT": "Time unit in time scheduling", "TIME_UNIT_ISO": "ISO code for unit of measurement", "NETWORK_PROFILE": "Network profile", "PROJECT_PROFILE": "Project Profile", "BUDGET_PROFILE": "Budget Profile", "PROJECT_STOCK": "Project stock", "OBJECTCLASS": "Object class, language-dependent", "STATISTICAL": "Statistical WBS element", "TAXJURCODE": "Tax Jurisdiction", "INTEREST_PROF": "Interest Profile for Project/Order Interest Calculation", "WBS_SCHED_PROFILE": "Profile for WBS scheduling", "INVEST_PROFILE": "Investment measure profile", "RES_ANAL_KEY": "Results Analysis Key", "PLAN_PROFILE": "Planning profile", "PLANINTEGRATED": "Indicator for Integrated Planning", "VALUATION_SPEC_STOCK": "Valuation of Special Stock", "SIMULATION_PROFILE": "Simulation profile", "GROUPING_INDICATOR": "Indicator: Automatic requirements grouping", "LOCATION": "Location", "PARTNER_PROFILE": "Partner Determination Procedure", "VENTURE": "Joint Venture", "REC_IND": "Recovery Indicator", "EQUITY_TYP": "Equity Type", "JV_OTYPE": "Joint Venture Object Type", "JV_JIBCL": "JIB/JIBE Class", "JV_JIBSA": "JIB/JIBE Subclass A", "SCHED_SCENARIO": "Scheduling scenario", "FCST_START": "Forecast start date of project definition", "FCST_FINISH": "Forecast finish date for project definition", "FUNC_AREA": "Functional Area", "SALESORG": "Sales Organization", "DISTR_CHAN": "Distribution Channel", "DIVISION": "Division", "DLI_PROFILE": "Dynamic Item Processor Profile", "LANGU": "Language Key", "LANGU_ISO": "2-Character SAP Language Code", "SYSTEM_STATUS": "System Status Text", "NETWORK_ASSIGNMENT": "Network assignment", "STAT_PROF": "Status Profile", "Extension": {"ZZKUNNR": "Customer Number", "ZZNAME1": "Name 1", "ZZEXT_REFID": "External Reference ID"}, "Partners": [{"ObjectNumber": "Object number", "PartnerFunction": "Partner Function", "Counter": "Counter for differentiation 6-digit", "ObjectCategory": "Object Category", "Partner": "Partner"}]}}