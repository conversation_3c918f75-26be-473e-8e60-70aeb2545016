import JoiBase from "joi";
import JoiDate from "@joi/date";

const Joi = JoiBase.extend(JoiDate);

const joischema = {
    ProjectPayload: Joi.object().keys({
        Payload: Joi.string().min(6).max(7).valid("Definition", "WBSElements", "Network").required()
    }),

    ProjectDefinitionQuery: Joi.object().keys({
        ProjectDefinition: Joi.string().min(1).max(24).required()
    }),

    ProjectNetworkQuery: Joi.object().keys({
        Network: Joi.string().min(1).max(12).required()
    }),
};

export default joischema;